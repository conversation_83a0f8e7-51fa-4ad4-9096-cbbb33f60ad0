#include "controllers/StaticController.hpp"
#include "core/Application.hpp"
#include "services/AuthService.hpp"
#include <spdlog/spdlog.h>

#ifdef EMBED_RESOURCES
#include "resources/EmbeddedResources.hpp"
#endif

StaticController::StaticController(Application &app)
    : app_(app)
{
    auto &server = app.getServer();
    
    // Serve index.html at root with authentication check
    server.Get("/", [this](const httplib::Request &req, httplib::Response &res) { 
        handleRoot(req, res); 
    });

    // Serve static files - but exclude API paths
    server.Get(R"(/(.+))", [this](const httplib::Request &req, httplib::Response &res) {
        handleStaticFile(req, res);
    });
}

void StaticController::handleRoot(const httplib::Request &req, httplib::Response &res)
{
    if (isAuthenticated(req))
    {
        serveFile("/index.html", res);
    }
    else
    {
        serveLoginPage(res);
    }
}

void StaticController::handleStaticFile(const httplib::Request &req, httplib::Response &res)
{
    auto matches = req.matches;
    std::string path = matches[1];

    // Don't serve API paths as static files
    if (path.substr(0, 4) == "api/")
    {
        res.status = 404;
        res.body = "API endpoint not found";
        return;
    }

    // Allow access to login.html without authentication
    if (path == "login.html")
    {
        serveLoginPage(res);
        return;
    }

    // Check authentication for other files
    if (!isAuthenticated(req))
    {
        res.status = 401;
        res.set_header("Content-Type", "text/html");
        res.body = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Unauthorized</title>
</head>
<body>
    <h1>401 - Unauthorized</h1>
    <p>Please <a href="/">login</a> to access this resource.</p>
</body>
</html>)";
        return;
    }

    serveFile("/" + path, res);
}

bool StaticController::isAuthenticated(const httplib::Request &req)
{
    std::string token = getSessionToken(req);
    if (token.empty())
    {
        return false;
    }

    auto auth_service = app_.getAuthService();
    return auth_service && auth_service->validateSession(token);
}

std::string StaticController::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty())
    {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos)
        {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}

void StaticController::serveLoginPage(httplib::Response &res)
{
#ifdef EMBED_RESOURCES
    // Use embedded login.html
    auto login_content = EmbeddedResources::getResourceContent("/login.html");
    if (!login_content.empty())
    {
        res.status = 200;
        res.set_header("Content-Type", "text/html");
        res.body = login_content;
        return;
    }
#endif

    // Fallback: serve a simple login page if embedded resource is not available
    res.status = 200;
    res.set_header("Content-Type", "text/html");
    res.body = R"(<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Report System - Login</title>
    <style>
        body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0; background: #f5f5f5; }
        .login-form { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 1rem; }
        label { display: block; margin-bottom: 0.5rem; }
        input { width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; }
        button { width: 100%; padding: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .error { color: red; margin-top: 1rem; text-align: center; display: none; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>Daily Report System</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" id="loginBtn">登录</button>
            <div class="error" id="errorMessage"></div>
        </form>
    </div>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');

            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            errorMessage.style.display = 'none';

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();
                if (result.code === 0) {
                    window.location.href = '/';
                } else {
                    errorMessage.textContent = result.message || '登录失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请重试';
                errorMessage.style.display = 'block';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
    </script>
</body>
</html>)";
}
