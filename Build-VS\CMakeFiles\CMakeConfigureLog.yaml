
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine version 16.11.2+f32259642 for .NET Framework
      Copyright (C) Microsoft Corporation. All rights reserved.
      
      Build started 6/24/2025 1:44:09 PM.
      Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.36
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/Code/DailyReport2/Build-VS/CMakeFiles/3.29.2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine version 16.11.2+f32259642 for .NET Framework
      Copyright (C) Microsoft Corporation. All rights reserved.
      
      Build started 6/24/2025 1:44:11 PM.
      Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\3.29.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.25
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Code/DailyReport2/Build-VS/CMakeFiles/3.29.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:64 (try_compile)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-vj9scl"
      binary: "D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-vj9scl"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-vj9scl'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/MSBuild/Current/Bin/MSBuild.exe" cmTC_e92fc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.2+f32259642 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 6/24/2025 1:44:13 PM.
        Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-vj9scl\\cmTC_e92fc.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e92fc.dir\\Debug\\".
          Creating directory "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-vj9scl\\Debug\\".
          Creating directory "cmTC_e92fc.dir\\Debug\\cmTC_e92fc.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e92fc.dir\\Debug\\cmTC_e92fc.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e92fc.dir\\Debug\\\\" /Fd"cmTC_e92fc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Local\\cmake\\share\\cmake-3.29\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e92fc.dir\\Debug\\\\" /Fd"cmTC_e92fc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\Local\\cmake\\share\\cmake-3.29\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-vj9scl\\Debug\\cmTC_e92fc.exe" /INCREMENTAL /ILK:"cmTC_e92fc.dir\\Debug\\cmTC_e92fc.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-vj9scl/Debug/cmTC_e92fc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-vj9scl/Debug/cmTC_e92fc.lib" /MACHINE:X64  /machine:x64 cmTC_e92fc.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_e92fc.vcxproj -> D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-vj9scl\\Debug\\cmTC_e92fc.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e92fc.dir\\Debug\\cmTC_e92fc.tlog\\unsuccessfulbuild".
          Touching "cmTC_e92fc.dir\\Debug\\cmTC_e92fc.tlog\\cmTC_e92fc.lastbuildstate".
        Done Building Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-vj9scl\\cmTC_e92fc.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.31
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:170 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:207 (cmake_determine_linker_id)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:64 (try_compile)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-lie7kg"
      binary: "D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-lie7kg"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-lie7kg'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/MSBuild/Current/Bin/MSBuild.exe" cmTC_b25a0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.2+f32259642 for .NET Framework
        Copyright (C) Microsoft Corporation. All rights reserved.
        
        Build started 6/24/2025 1:44:15 PM.
        Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-lie7kg\\cmTC_b25a0.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b25a0.dir\\Debug\\".
          Creating directory "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-lie7kg\\Debug\\".
          Creating directory "cmTC_b25a0.dir\\Debug\\cmTC_b25a0.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b25a0.dir\\Debug\\cmTC_b25a0.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b25a0.dir\\Debug\\\\" /Fd"cmTC_b25a0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\Local\\cmake\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.29.30159 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          CMakeCXXCompilerABI.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_b25a0.dir\\Debug\\\\" /Fd"cmTC_b25a0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\Local\\cmake\\share\\cmake-3.29\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-lie7kg\\Debug\\cmTC_b25a0.exe" /INCREMENTAL /ILK:"cmTC_b25a0.dir\\Debug\\cmTC_b25a0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-lie7kg/Debug/cmTC_b25a0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Code/DailyReport2/Build-VS/CMakeFiles/CMakeScratch/TryCompile-lie7kg/Debug/cmTC_b25a0.lib" /MACHINE:X64  /machine:x64 cmTC_b25a0.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_b25a0.vcxproj -> D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-lie7kg\\Debug\\cmTC_b25a0.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b25a0.dir\\Debug\\cmTC_b25a0.tlog\\unsuccessfulbuild".
          Touching "cmTC_b25a0.dir\\Debug\\cmTC_b25a0.tlog\\cmTC_b25a0.lastbuildstate".
        Done Building Project "D:\\Code\\DailyReport2\\Build-VS\\CMakeFiles\\CMakeScratch\\TryCompile-lie7kg\\cmTC_b25a0.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.29
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:170 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/Local/cmake/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:207 (cmake_determine_linker_id)"
      - "D:/Local/cmake/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
