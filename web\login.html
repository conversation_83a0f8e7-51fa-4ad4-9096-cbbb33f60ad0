<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Report System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: #f4f6f8;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            line-height: 1.6;
            color: #2c3e50;
        }

        .login-container {
            background: #fff;
            padding: 2rem;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            background: #34495e;
            color: #ecf0f1;
            padding: 1.5rem;
            margin: -2rem -2rem 2rem -2rem;
        }

        .login-header h1 {
            color: #ecf0f1;
            margin-bottom: 0.5rem;
            font-size: 20px;
            font-weight: 600;
        }

        .login-header p {
            color: #95a5a6;
            font-size: 13px;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 500;
            font-size: 13px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            font-size: 13px;
            font-family: inherit;
            background: #fff;
            color: #2c3e50;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            background: #fff;
        }

        .login-btn {
            width: 100%;
            padding: 12px 16px;
            background: #3498db;
            color: #fff;
            border: 1px solid #3498db;
            border-radius: 0;
            font-size: 13px;
            font-weight: 500;
            font-family: inherit;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
        }

        .login-btn:hover {
            background: #2980b9;
            border-color: #2980b9;
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #3498db;
            border-color: #3498db;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 13px;
            margin-top: 1rem;
            text-align: center;
            display: none;
            padding: 8px 12px;
            background: #fadbd8;
            border: 1px solid #e74c3c;
            border-radius: 0;
        }

        .success-message {
            color: #27ae60;
            font-size: 13px;
            margin-top: 1rem;
            text-align: center;
            display: none;
            padding: 8px 12px;
            background: #d5f4e6;
            border: 1px solid #27ae60;
            border-radius: 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Daily Report System</h1>
            <p>请输入您的登录凭据</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // Reset messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // Disable button
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    successMessage.textContent = '登录成功，正在跳转...';
                    successMessage.style.display = 'block';
                    
                    // Redirect to main page after successful login
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    errorMessage.textContent = result.message || '登录失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请重试';
                errorMessage.style.display = 'block';
            } finally {
                // Re-enable button
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
    </script>
</body>
</html>
