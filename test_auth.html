<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Daily Report System - Authentication Test</h1>
    
    <div class="test-section">
        <h2>1. Test Login API</h2>
        <div>
            <input type="text" id="username" placeholder="Username" value="admin">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Auth Check API</h2>
        <button onclick="testAuthCheck()">Check Authentication Status</button>
        <div id="authCheckResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Protected API (Products)</h2>
        <button onclick="testProtectedAPI()">Test Products API</button>
        <div id="protectedAPIResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Logout API</h2>
        <button onclick="testLogout()">Test Logout</button>
        <div id="logoutResult" class="result"></div>
    </div>

    <script>
        function showResult(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                const message = `Status: ${response.status}, Response: ${JSON.stringify(result)}`;
                showResult('loginResult', message, response.ok && result.code === 0);
            } catch (error) {
                showResult('loginResult', `Error: ${error.message}`, false);
            }
        }

        async function testAuthCheck() {
            try {
                const response = await fetch('/api/auth/check');
                const result = await response.json();
                const message = `Status: ${response.status}, Response: ${JSON.stringify(result)}`;
                showResult('authCheckResult', message, response.ok);
            } catch (error) {
                showResult('authCheckResult', `Error: ${error.message}`, false);
            }
        }

        async function testProtectedAPI() {
            try {
                const response = await fetch('/api/products');
                const result = await response.json();
                const message = `Status: ${response.status}, Response: ${JSON.stringify(result, null, 2)}`;
                showResult('protectedAPIResult', message, response.ok && result.code === 0);
            } catch (error) {
                showResult('protectedAPIResult', `Error: ${error.message}`, false);
            }
        }

        async function testLogout() {
            try {
                const response = await fetch('/api/auth/logout', {
                    method: 'POST'
                });
                const result = await response.json();
                const message = `Status: ${response.status}, Response: ${JSON.stringify(result)}`;
                showResult('logoutResult', message, response.ok && result.code === 0);
            } catch (error) {
                showResult('logoutResult', `Error: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
