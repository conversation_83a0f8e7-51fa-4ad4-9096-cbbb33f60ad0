﻿cmake_minimum_required(VERSION 3.20)
project(daily_report VERSION 0.1.0)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

find_package(n<PERSON>hmann_json CONFIG REQUIRED)
find_package(httplib CONFIG REQUIRED)
find_package(SQLiteCpp CONFIG REQUIRED)
find_package(spdlog CONFIG REQUIRED)
find_package(OpenSSL REQUIRED)

include_directories(include)

# Enable HTTPS support for httplib
add_definitions(-DCPPHTTPLIB_OPENSSL_SUPPORT)

# Get source files (excluding the placeholder EmbeddedResources.cpp)
file(GLOB_RECURSE SRC_FILES
    "src/*.cpp"
    "include/*.hpp"
)

# Remove the placeholder EmbeddedResources.cpp from source files
list(FILTER SRC_FILES EXCLUDE REGEX "src/resources/EmbeddedResources\\.cpp$")

# Include our custom CMake module
include(${CMAKE_CURRENT_SOURCE_DIR}/cmake/EmbedResources.cmake)

# Set path for generated resources header
set(EMBEDDED_RESOURCES_HEADER "${CMAKE_CURRENT_BINARY_DIR}/include/resources/EmbeddedResources.hpp")

# Create output directory
file(MAKE_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/include/resources")

# Get all web files
file(GLOB_RECURSE WEB_FILES "${CMAKE_CURRENT_SOURCE_DIR}/web/*")

# Generate embedded resources using function call
embed_resources("${EMBEDDED_RESOURCES_HEADER}" "${CMAKE_CURRENT_SOURCE_DIR}/web")

# No need to add source files since everything is inline in the header

# Include generated header directory
include_directories("${CMAKE_CURRENT_BINARY_DIR}/include")

# Define EMBED_RESOURCES macro
add_compile_definitions(EMBED_RESOURCES)

# Create executable
add_executable(${PROJECT_NAME} ${SRC_FILES})



if(WIN32)
    target_compile_options(${PROJECT_NAME} PRIVATE /bigobj /utf-8)
endif()

target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json::nlohmann_json
    httplib::httplib
    SQLiteCpp
    spdlog::spdlog_header_only
    OpenSSL::SSL OpenSSL::Crypto
)

# Copy configuration file to build directory
# add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#     COMMAND ${CMAKE_COMMAND} -E copy_if_different
#     ${CMAKE_SOURCE_DIR}/config.json
#     $<TARGET_FILE_DIR:${PROJECT_NAME}>/config.json
#     COMMENT "Copying config.json to build directory"
# )