#pragma once

#include "services/AuthService.hpp"
#include <httplib.h>
#include <nlohmann/json.hpp>

class Application;

class AuthController
{
public:
    AuthController(Application &app);

private:
    void login(const httplib::Request &req, httplib::Response &res);
    void logout(const httplib::Request &req, httplib::Response &res);
    void checkAuth(const httplib::Request &req, httplib::Response &res);

    Application &app_;
    std::shared_ptr<AuthService> auth_service_;

    std::string getSessionToken(const httplib::Request &req);
    void setSessionCookie(httplib::Response &res, const std::string &token);
    void clearSessionCookie(httplib::Response &res);
};
